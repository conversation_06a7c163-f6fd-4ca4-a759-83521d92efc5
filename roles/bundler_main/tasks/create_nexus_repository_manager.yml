# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_nexus_repository_manager.yml                               #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_nexus_repository_manager | Create nexus directory
  ansible.builtin.file:
    path: "{{ nexus_data_dir }}"
    state: directory
    mode: '0755'

- name: create_nexus_repository_manager | Start nexus container
  containers.podman.podman_container:
    name: "{{ container_name }}"
    image: "{{ nexus_image }}"
    env:
      INSTALL4J_ADD_VM_PARAMS: -Xms2703m -Xmx2703m
        -XX:MaxDirectMemorySize=2703m
        -Djava.util.prefs.userRoot=/nexus-data/javaprefs
        -Dcom.redhat.fips=false
    uidmap:
      - "0:1:200"
      - "200:0:1"
      - "201:201:10000"
    state: started
    network: slirp4netns
    publish:
      - "{{ bundler.nexus_http_port }}:{{ bundler.nexus_http_port }}"
      - "{{ bundler.registry_port }}:{{ bundler.registry_port }}"
    volumes:
      - "{{ nexus_data_dir }}:/nexus-data:z"
  environment:
    NO_PROXY: "{{ no_proxy }}"
    no_proxy: "{{ no_proxy }}"

- name: create_nexus_repository_manager | Wait for nexus to become active at url {{ galaxy_nexus_api + '/status' }}
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/status"
    method: "GET"
    return_content: true
  register: nexus_response
  retries: 50
  delay: 10
  until: nexus_response.status == 200
