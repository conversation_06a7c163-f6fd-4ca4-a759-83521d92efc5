# Bundler Main Role - Complete Documentation

## Overview

The `bundler_main` role is a comprehensive Ansible role designed to create OpenShift/OKD deployment bundles for air-gapped environments. It packages all necessary components including container images, installation files, and configuration into a portable bundle that can be deployed in disconnected networks.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Role Structure](#role-structure)
3. [Key Components](#key-components)
4. [Task Flow](#task-flow)
5. [Variables and Configuration](#variables-and-configuration)
6. [Dependencies](#dependencies)
7. [Usage Examples](#usage-examples)
8. [Troubleshooting](#troubleshooting)

## Architecture Overview

```
Internet Sources → Bundler Main Role → Local Nexus Registry
                                   → Package Files
                                   → Bundle Archive

Sources:
- Harbor Registry (harbor.global.lmco.com)
- Red Hat Registry (registry.redhat.io)
- OpenShift Mirrors
- GitHub Releases

Local Components:
- Nexus Registry (Docker Images, OCP Images, Additional Images)
- Package Files (ISO Files, Installation Files, Configuration Files)
- Bundle Archive → Air-Gapped Environment
```

## Role Structure

```
roles/bundler_main/
├── defaults/
│   └── main.yml                    # Default variables
├── files/
│   └── default-additonal-images.yaml  # Additional container images list
├── tasks/
│   ├── main.yml                    # Main task orchestration
│   ├── configure_nexus.yml         # Nexus repository setup
│   ├── create_certs.yml            # SSL certificate generation
│   ├── create_nexus_repository_manager.yml  # Nexus container setup
│   ├── download_bundle_files.yml   # Download external files
│   ├── get_coreos_files.yml        # CoreOS image handling
│   ├── get_dnf_repo_files.yml      # DNF repository creation
│   ├── mirror_registry_to_nexus.yml # Container image mirroring
│   ├── package_nexus_data.yml      # Bundle packaging
│   ├── pull_tag_image_registry_files_from_cache.yml  # Image caching
│   ├── setup_container.yml         # Container environment setup
│   ├── setup_haproxy.yml          # HAProxy configuration
│   └── upload_to_nexus.yml         # File upload to Nexus
├── templates/
│   ├── big-bang.sh.j2              # Deployment script template
│   ├── config.json.j2              # Docker config template
│   ├── dynamic-image-set-configuration.yaml.j2  # OC mirror config
│   ├── generic-0.j2                # Image content source policy
│   ├── haproxy.cfg.j2              # HAProxy configuration
│   ├── image-set-configuration.yaml.j2  # Static mirror config
│   └── openssl.conf.j2             # SSL configuration
└── vars/
    └── main.yml                    # Role variables
```

## Key Components

### 1. Nexus Repository Manager
- **Purpose**: Acts as a local artifact repository and container registry
- **Components**: 
  - Docker registry for container images
  - Raw repositories for files
  - Proxy repositories for external sources
- **Configuration**: Automated setup with security and repository creation

### 2. Container Image Mirroring
- **Source Registries**:
  - Harbor: `harbor.global.lmco.com`
  - Red Hat: `registry.redhat.io`
  - Quay: `quay.io`
  - Docker Hub: `docker.io`
- **Process**: Downloads, retags, and pushes to local Nexus registry
- **Tools**: Uses `oc mirror` and `skopeo` for image operations

### 3. HAProxy Load Balancer
- **Purpose**: Provides SSL termination and domain routing
- **Features**:
  - Routes external registry requests to Nexus
  - SSL certificate management
  - Proxy configuration for air-gapped environments

### 4. Bundle Packaging
- **Contents**:
  - Nexus data directory with all artifacts
  - Installation scripts and configurations
  - SSL certificates and keys
  - Deployment automation scripts

## Task Flow

### Phase 1: Environment Setup
```yaml
# 1. Container Setup (setup_container.yml)
- Create directories
- Configure /etc/hosts
- Set up Docker authentication

# 2. Certificate Creation (create_certs.yml)  
- Generate SSL certificates
- Configure certificate authorities
- Set up secure communications
```

### Phase 2: Repository Infrastructure
```yaml
# 3. Nexus Setup (create_nexus_repository_manager.yml)
- Start Nexus container
- Wait for service availability
- Configure initial settings

# 4. Nexus Configuration (configure_nexus.yml)
- Create repositories (Docker, Raw, Proxy)
- Set up security and users
- Configure repository policies

# 5. HAProxy Setup (setup_haproxy.yml)
- Configure load balancer
- Set up SSL termination
- Create domain routing rules
```

### Phase 3: Content Collection
```yaml
# 6. File Downloads (download_bundle_files.yml)
- Download Galaxy container image
- Fetch static files from Nexus
- Collect additional files

# 7. CoreOS Files (get_coreos_files.yml) [Baremetal only]
- Download CoreOS images
- Configure boot images
- Set up installation media

# 8. DNF Repositories (get_dnf_repo_files.yml)
- Create RHEL package repositories
- Extract ISO contents
- Generate repository metadata
```

### Phase 4: Container Image Mirroring
```yaml
# 9. Registry Mirroring (mirror_registry_to_nexus.yml)
- Configure authentication
- Set up image set configuration
- Mirror OpenShift images
- Mirror additional container images

# 10. Image Caching (pull_tag_image_registry_files_from_cache.yml)
- Pull images from Harbor
- Retag for local registry
- Push to Nexus registry
```

### Phase 5: Bundle Creation
```yaml
# 11. Upload Files (upload_to_nexus.yml)
- Upload collected files to Nexus
- Organize repository structure
- Verify uploads

# 12. Package Bundle (package_nexus_data.yml)
- Create bundle archive
- Include deployment scripts
- Generate checksums
- Prepare for distribution
```

## Variables and Configuration

### Core Variables (vars/main.yml)

```yaml
# Image Sources
galaxy_image: "harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:{{ galaxy.version }}"
nexus_image: "docker.io/sonatype/nexus3:{{ bundler.nexus_image_version }}"

# Network Configuration
default_registry_url: "{{ bundler.default_registry_url }}"
default_nexus_url: "https://{{ bundler.default_nexus_url }}"

# Proxy Settings
no_proxy: "{{ (bundler.standard_no_proxy_addresses + ((domain_mappings | map(attribute='destination')) if proxying_mirror else [])) | join(',') }}"

# Domain Mappings for Air-Gapped Environments
domain_mappings:
  - destination: docker.io
    reroute: docker-io-nexus.{{ nexus_base_url }}
  - destination: harbor.global.lmco.com
    reroute: harbor-global-nexus.{{ nexus_base_url }}
  # ... additional mappings
```

### Bundler Configuration (bundler_common/vars/main.yml)

```yaml
default_bundler:
  # Proxy Configuration
  http_proxy: http://proxy-zsgov.external.lmco.com:80
  https_proxy: http://proxy-zsgov.external.lmco.com:80

  # Authentication
  nexus_user: "{{ lookup('env', 'NEXUS_USER') }}"
  nexus_password: "{{ lookup('env', 'NEXUS_PASSWORD') }}"

  # OpenShift Configuration
  openshift_version: "4.14.0"
  distro: "ocp"  # ocp, okd, or sno
  platform: "baremetal"  # baremetal or cloud
```

### Required Environment Variables

```bash
# Nexus Authentication
export NEXUS_USER="admin"
export NEXUS_PASSWORD="your-password"
export NEXUS_URL="nexus.example.com"

# Harbor Registry Authentication
export HARBOR_US_USER="your-harbor-user"
export HARBOR_US_USER_CLI_SECRET="your-harbor-password"

# Red Hat Registry (if not using proxy)
export REDHAT_REGISTRY_USER="your-rh-user"
export REDHAT_REGISTRY_PASSWORD="your-rh-password"
```

## Dependencies

### External Tools Required
- **Ansible**: 2.9+
- **Podman**: Container runtime
- **Skopeo**: Container image operations
- **oc**: OpenShift CLI with mirror plugin
- **HAProxy**: Load balancing
- **OpenSSL**: Certificate generation

### Ansible Collections
```yaml
collections:
  - containers.podman
  - ansible.posix
  - community.general
```

### System Requirements
- **Storage**: 500GB+ for bundle creation
- **Memory**: 8GB+ RAM
- **Network**: Internet access for downloads
- **OS**: RHEL 8/9 or compatible

## Usage Examples

### Basic Bundle Creation

```yaml
---
- name: Create OpenShift Bundle
  hosts: localhost
  vars:
    bundler:
      openshift_version: "4.14.0"
      distro: "ocp"
      platform: "baremetal"
      nexus_domain: "nexus.example.com"
      default_registry_url: "registry.example.com"
      default_nexus_url: "nexus.example.com"
  roles:
    - bundler_main
```

### Advanced Configuration

```yaml
---
- name: Create Custom Bundle
  hosts: localhost
  vars:
    bundler:
      openshift_version: "4.14.0"
      distro: "ocp"
      platform: "baremetal"

      # Custom image versions
      nexus_image_version: "3.70.1"

      # Additional files to include
      additional_files: "/path/to/custom/files"

      # Network configuration
      http_proxy: "http://proxy.company.com:8080"
      https_proxy: "http://proxy.company.com:8080"

      # Storage backend
      storage_backend: "nexus"

    # Override default behavior
    proxying_mirror: true
    include_previous_ocp_versions: false

  roles:
    - bundler_main
```

### GitLab CI Integration

```yaml
generate_bundle:
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest
  script:
    - ansible-playbook -i localhost lmco.bundler.create_bundle.yml
      -e "bundler={'openshift_version':'4.14.0','distro':'ocp'}"
      -e "bundle_name='my-cluster'"
  artifacts:
    paths:
      - /opt/package_files/
    expire_in: 7 days
```

## Troubleshooting

### Common Issues

#### 1. Harbor Registry Connection Refused
```bash
# Error: dial tcp 127.0.0.1:443: connect: connection refused

# Solution: Check proxy configuration
export HTTP_PROXY="http://proxy-zsgov.external.lmco.com:80"
export HTTPS_PROXY="http://proxy-zsgov.external.lmco.com:80"
export NO_PROXY=".lmco.com,localhost,127.0.0.1"

# Test connectivity
curl --proxy "$HTTP_PROXY" -I https://harbor.global.lmco.com/v2/
```

#### 2. Nexus Authentication Failures
```bash
# Check environment variables
echo $NEXUS_USER
echo $NEXUS_PASSWORD

# Verify Nexus is running
podman ps | grep nexus

# Check Nexus logs
podman logs nexus
```

#### 3. Image Mirroring Failures
```bash
# Check oc mirror logs
tail -f /opt/openshift/mirror-redhat2galaxy/.oc-mirror.log

# Verify authentication
skopeo login registry.redhat.io
skopeo login harbor.global.lmco.com

# Test image access
skopeo inspect docker://registry.redhat.io/ubi8/ubi:latest
```

#### 4. Storage Space Issues
```bash
# Check available space
df -h /opt

# Clean up old containers
podman system prune -a

# Monitor space during bundle creation
watch "df -h /opt && du -sh /opt/nexus-data"
```

### Debug Mode

Enable verbose logging:
```yaml
- name: Run with debug
  include_role:
    name: bundler_main
  vars:
    ansible_verbosity: 2
    debug_mode: true
```

### Log Locations

```bash
# Nexus logs
/opt/openshift/nexus-data/log/

# HAProxy logs
/var/log/haproxy.log

# Ansible logs
/tmp/ansible.log

# Container logs
podman logs nexus
podman logs haproxy
```

## Security Considerations

### Credential Management

- Store sensitive variables in Ansible Vault
- Use environment variables for CI/CD
- Rotate credentials regularly
- Limit access to bundle creation systems

### Network Security

- Use SSL/TLS for all communications
- Configure proper firewall rules
- Implement network segmentation
- Monitor access logs

### Bundle Security

- Encrypt bundle archives
- Verify checksums before deployment
- Scan container images for vulnerabilities
- Maintain audit trails

## Performance Optimization

### Parallel Operations
```yaml
# Enable parallel image pulls
vars:
  max_parallel_downloads: 5

# Use multiple mirror workers
oc_mirror_max_per_registry: 10
```

### Storage Optimization
```yaml
# Use fast storage for temporary files
vars:
  temp_directory: "/fast-storage/tmp"

# Enable compression
bundle_compression: true
```

### Network Optimization
```yaml
# Increase timeouts for slow networks
vars:
  download_timeout: 1800
  registry_timeout: 600

# Configure retry logic
max_retries: 5
retry_delay: 30
```

## Conclusion

This documentation provides a comprehensive understanding of the `bundler_main` role, its components, configuration options, and operational procedures. Use it as a reference for deployment, troubleshooting, and customization of your OpenShift bundle creation process.

For additional support or questions, refer to the project documentation or contact the development team.
