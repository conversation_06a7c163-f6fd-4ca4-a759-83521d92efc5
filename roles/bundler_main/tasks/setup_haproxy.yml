# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_haproxy.yml                                                 #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: setup_haproxy | Create nexus certs
  ansible.builtin.include_tasks: create_certs.yml
  vars:
    cert_directory: "{{ apps_install_path }}/certs"
    dns_name: galaxy.lmco.com
    alt_names: "{{ [bundler.default_nexus_url, bundler.default_registry_url] + (domain_mappings | map(attribute='destination')) }}"

- name: setup_haproxy | Run root required modules
  become: true
  become_user: root
  block:
    - name: setup_haproxy | Install haproxy packages
      ansible.builtin.yum:
        name: haproxy
        state: present

    - name: setup_haproxy | Create dns haproxy.cfg file
      ansible.builtin.template:
        src: haproxy.cfg.j2
        dest: /etc/haproxy/haproxy.cfg
        mode: '0644'
        lstrip_blocks: true

    - name: setup_haproxy | Restart haproxy service
      ansible.builtin.command:
        cmd: haproxy -f /etc/haproxy/haproxy.cfg
      changed_when: true
