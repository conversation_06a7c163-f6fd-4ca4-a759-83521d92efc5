#!/bin/bash

# Test script to verify Harbor registry connectivity
# This script tests the same network conditions that your GitLab CI pipeline will encounter

echo "=== Harbor Registry Connection Test ==="
echo "Date: $(date)"
echo

# Check if environment variables are set (optional for this test)
echo "1. Checking environment variables..."
if [ -z "$HARBOR_US_USER" ]; then
    echo "   ⚠️  HARBOR_US_USER is not set (optional for connectivity test)"
else
    echo "   ✅ HARBOR_US_USER is set: $HARBOR_US_USER"
fi

if [ -z "$HARBOR_US_USER_CLI_SECRET" ]; then
    echo "   ⚠️  HARBOR_US_USER_CLI_SECRET is not set (optional for connectivity test)"
else
    echo "   ✅ HARBOR_US_USER_CLI_SECRET is set: [HIDDEN]"
fi

echo

# Set proxy variables (matching your GitLab CI configuration)
export HTTP_PROXY="http://proxy-zsgov.external.lmco.com:80"
export HTTPS_PROXY="http://proxy-zsgov.external.lmco.com:80"
export NO_PROXY=".lmco.com,localhost,127.0.0.1"

echo "2. Testing network connectivity..."
echo "   Proxy settings:"
echo "   - HTTP_PROXY: $HTTP_PROXY"
echo "   - HTTPS_PROXY: $HTTPS_PROXY"
echo "   - NO_PROXY: $NO_PROXY"

# Test basic connectivity to Harbor registry
echo "   Testing Harbor registry connectivity..."
if curl --proxy "$HTTP_PROXY" -s -I https://harbor.global.lmco.com/v2/ | head -1 | grep -q "200\|401\|403"; then
    echo "   ✅ Harbor registry is reachable through proxy"
    PROXY_WORKS=true
else
    echo "   ❌ Harbor registry is not reachable through proxy"
    echo "   Trying direct connection..."
    if curl -s -I https://harbor.global.lmco.com/v2/ | head -1 | grep -q "200\|401\|403"; then
        echo "   ✅ Harbor registry is reachable directly"
        DIRECT_WORKS=true
    else
        echo "   ❌ Harbor registry is not reachable directly either"
    fi
fi

echo

# Test skopeo with proxy
echo "3. Testing skopeo with proxy settings..."
if command -v skopeo >/dev/null 2>&1; then
    echo "   ✅ skopeo is available"

    # Test basic skopeo connectivity with proxy
    echo "   Testing skopeo connectivity to Harbor registry..."
    if HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
       skopeo inspect docker://harbor.global.lmco.com/v2/ --raw >/dev/null 2>&1; then
        echo "   ✅ skopeo can connect to Harbor registry with proxy"
    else
        echo "   ❌ skopeo cannot connect to Harbor registry with proxy"
        echo "   This is the same error your GitLab CI is experiencing"
    fi

    # If credentials are available, test authentication
    if [ -n "$HARBOR_US_USER" ] && [ -n "$HARBOR_US_USER_CLI_SECRET" ]; then
        echo "   Testing skopeo authentication..."
        if HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
           skopeo login harbor.global.lmco.com --username "$HARBOR_US_USER" --password "$HARBOR_US_USER_CLI_SECRET" 2>/dev/null; then
            echo "   ✅ skopeo authentication successful"
        else
            echo "   ❌ skopeo authentication failed"
        fi
    else
        echo "   ⚠️  Skipping authentication test (credentials not provided)"
    fi
else
    echo "   ❌ skopeo is not available"
    echo "   Please install skopeo to test the exact same tool your pipeline uses"
fi

echo

# Summary
echo "=== Test Summary ==="
echo "This test simulates the network environment your GitLab CI pipeline will use."
echo
if [ "$PROXY_WORKS" = true ] || [ "$DIRECT_WORKS" = true ]; then
    echo "✅ Network connectivity to Harbor registry is working"
    echo "   Your GitLab CI should be able to connect to Harbor"
else
    echo "❌ Network connectivity to Harbor registry is failing"
    echo "   This is likely the same issue your GitLab CI is experiencing"
    echo
    echo "Troubleshooting suggestions:"
    echo "1. Check if the proxy server is accessible from your network"
    echo "2. Verify DNS resolution for harbor.global.lmco.com"
    echo "3. Check if there are firewall rules blocking the connection"
fi
echo
echo "Next steps:"
echo "1. Commit the updated Ansible roles (they now include proxy settings and retries)"
echo "2. Run your existing GitLab CI pipeline"
echo "3. Monitor the 'generate_galaxy_bundle' job for the connectivity test output"
echo

echo "=== Test Complete ==="
