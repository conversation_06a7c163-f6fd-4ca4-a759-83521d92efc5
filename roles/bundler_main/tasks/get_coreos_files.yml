# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        get_coreos_files.yml                                              #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get_coreos_files | Get coreos image uri
  lmco.bundler.get_coreos_uri:
    coreos_property: location
  register: coreos_qemu_uri

- name: get_coreos_files | Get coreos image sha
  lmco.bundler.get_coreos_uri:
    coreos_property: uncompressed-sha256
  register: coreos_sha256

- name: get_coreos_files | Create yaml variables file for holding coreos values
  ansible.builtin.template:
    src: ocp_variables.yaml.j2
    dest: "{{ package_files_galaxy_folder }}/{{ bundler.verson_vars_file }}"
    mode: '0644'

- name: get_coreos_files | Download coreos image if it has not been already
  ansible.builtin.get_url:
    url: "{{ coreos_qemu_uri.value | replace('rhcos.mirror.openshift.com', rhcos_redirect) | replace('builds.coreos.fedoraproject.org', fcos_redirect) }}"
    dest: "{{ galaxy_nexus_repo_files_dir }}/{{ coreos_qemu_name }}"
    mode: '0644'
    timeout: 600
  vars:
    rhcos_redirect: "{{ bundler.nexus_domain }}/repository/rhcos-mirror"
    fcos_redirect: "{{ bundler.nexus_domain }}/repository/fcos-mirror"
