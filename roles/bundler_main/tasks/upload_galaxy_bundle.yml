# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        upload_galaxy_bundle.yml                                          #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: upload_galaxy_bundle | Set galaxy bundle filename with timestamp
  ansible.builtin.set_fact:
    galaxy_bundle_filename: "galaxy_bundle_{{ bundle_name }}_v{{ galaxy.version }}_{{ '%Y-%m-%d_%H-%M' | strftime }}.tar"

- name: upload_galaxy_bundle | Get all files
  ansible.builtin.find:
    paths:
      - "{{ package_files }}"
    file_type: file
    recurse: true
  register: files_to_upload

- name: upload_galaxy_bundle | Archive files in {{ package_files }}
  community.general.archive:
    path: "{{ files_to_upload.files | map(attribute='path') }}"
    dest: "{{ package_files }}/{{ galaxy_bundle_filename }}"
    format: tar
    mode: '0644'

- name: upload_galaxy_bundle | Relieve all the space
  ansible.builtin.file:
    state: absent
    path: "{{ file_to_delete }}"
  loop: "{{ files_to_upload.files | map(attribute='path') }}"
  loop_control:
    loop_var: file_to_delete
  when: not bundler.developer_run

- name: upload_galaxy_bundle | Set FIPS to NO-ENFORCE-EMS for s3 upload
  ansible.builtin.command:
    cmd: update-crypto-policies --set FIPS:NO-ENFORCE-EMS
  changed_when: true
  become: true
  become_user: root
  when: storage_backend == 's3'

- name: upload_galaxy_bundle | Upload bundle to s3
  amazon.aws.s3_object:
    aws_ca_bundle: "{{ bundler.galaxy_ca_cert }}"
    bucket: "{{ bundler.aws_s3_bucket }}"
    access_key: "{{ bundler.aws_access_key_id }}"
    secret_key: "{{ bundler.aws_secret_access_key }}"
    region: "{{ bundler.aws_default_region }}"
    endpoint_url: "{{ bundler.aws_s3_endpoint_url }}"
    src: "{{ package_files }}/{{ galaxy_bundle_filename }}"
    object: "{{ bundler.business_area }}/{{ bundler.program }}/{{ galaxy.version }}/{{ bundler.bundle_s3_folder_name }}/{{ galaxy_bundle_filename }}"
    mode: put
    validate_certs: "{{ bundler.validate_certs }}"
  retries: 3
  delay: 3
  register: aws_s3_sync_output
  until: aws_s3_sync_output.changed
  when: storage_backend == 's3'
  environment:
    HTTPS_PROXY: "{{ bundler.http_proxy }}"
    HTTP_PROXY: "{{ bundler.http_proxy }}"
    NO_PROXY: "{{ no_proxy }}"

- name: upload_galaxy_bundle | Upload bundle to nexus
  ansible.builtin.uri:
    url: "https://{{ bundler.nexus_domain }}/{{ bundler.nexus_bundle_path }}/{{ galaxy_bundle_filename }}"
    method: PUT
    user: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    headers:
      Content-Type: application/x-tar
    src: "{{ package_files }}/{{ galaxy_bundle_filename }}"
    force_basic_auth: true
    remote_src: true
    status_code:
      - 201
  when: storage_backend == 'nexus'

- name: upload_galaxy_bundle | Get list of bundles from nexus
  ansible.builtin.uri:
    url: https://{{ bundler.nexus_domain }}/service/rest/v1/search?repository=space-station&group=%2Fbundles
    method: "GET"
    user: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    force_basic_auth: true
    return_content: true
  register: bundle_response
  when: storage_backend == 'nexus'

- name: upload_galaxy_bundle | Delete all previous bundles
  ansible.builtin.uri:
    url: https://{{ bundler.nexus_domain }}/service/rest/v1/components/{{ id_to_delete }}
    method: DELETE
    user: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    force_basic_auth: true
    status_code:
      - 204
  loop: "{{ list_of_galaxy_bundle_ids_to_delete }}"
  loop_control:
    loop_var: id_to_delete
  when: storage_backend == 'nexus'
  vars:
    list_of_galaxy_bundle_ids_to_delete: >-
      {{
        bundle_response.json['items'] |
        selectattr('name', 'contains', bundle_name) |
        rejectattr('name', 'contains', galaxy_bundle_filename) |
        map(attribute='id')
      }}
