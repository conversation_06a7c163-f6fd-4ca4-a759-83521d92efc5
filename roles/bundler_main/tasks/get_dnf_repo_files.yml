# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        get_dnf_repo_files.yml                                            #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: get_dnf_repo_files | Create new modified iso folder
  ansible.builtin.file:
    path: "{{ modified_iso_location }}"
    state: directory
    mode: "0755"

- name: get_dnf_repo_files | Create new nexus_files directory
  ansible.builtin.file:
    path: "{{ galaxy_nexus_repo_files_dir }}/{{ iso_data.name }}"
    state: directory
    mode: "0755"

- name: get_dnf_repo_files | Download rhel iso from nexus
  ansible.builtin.get_url:
    url: "https://{{ bundler.nexus_domain }}/{{ bundler.nexus_ISO_path }}/{{ iso_data.iso }}"
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    dest: "/tmp/{{ iso_data.iso }}"
    mode: "0644"
    timeout: 600

- name: get_dnf_repo_files | Unarchive iso files to modified iso directory
  ansible.builtin.command:
    cmd: bsdtar -xf /tmp/{{ iso_data.iso }} -C {{ galaxy_nexus_repo_files_dir }}/{{ iso_data.name }}/ {{ item }}
  changed_when: true
  loop: '{{ iso_data.repos }}'

- name: get_dnf_repo_files | Add write access for owner of all files in modified iso
  ansible.builtin.file:
    path: "{{ galaxy_nexus_repo_files_dir }}/{{ iso_data.name }}/{{ item }}"
    mode: u+w
    recurse: true
  loop: '{{ iso_data.repos }}'

# DO NOT DELETE This is the command to get the packages dnf download --urls --quiet $(dnf repoquery --installed --quiet) | awk -F/ '{print $NF}'
- name: get_dnf_repo_files | Copy packages file
  ansible.builtin.copy:
    src: "{{ packages_file_name }}"
    dest: "{{ packages_file }}"
    mode: "0644"

- name: get_dnf_repo_files | Delete unnecessary packages from iso
  ansible.builtin.shell:
    cmd: set -o pipefail && find {{ galaxy_nexus_repo_files_dir }}/{{ iso_data.name }}/{{ item }}/Packages -type f | grep -Evf {{ packages_file }} | xargs rm -f
  changed_when: true
  loop: '{{ iso_data.repos }}'

- name: get_dnf_repo_files | Generate included RPM list
  ansible.builtin.find:
    paths: '{{ galaxy_nexus_repo_files_dir }}/{{ iso_data.name }}/{{ item }}/Packages'
  register: included_rpms
  loop: '{{ iso_data.repos }}'

- name: get_dnf_repo_files | Write out RHEL SBOM
  ansible.builtin.include_tasks: create_sbom.yml
  vars:
    spdx_file: '{{ iso_data.sdx_file }}'
    filtered_packages: '{{ included_rpms.results | map(attribute="files") | flatten | map(attribute="path") | map("regex_replace", ".*/", "") }}'
    version: '{{ iso_data.version }}'

- name: get_dnf_repo_files | Create galaxy files tarball
  community.general.archive:
    path: "{{ iso_data.repos | map('regex_replace', '^', galaxy_nexus_repo_files_dir + '/' + iso_data.name + '/') | list }}"
    dest: "{{ package_files_galaxy_folder }}/{{ bundler.bastion_rhel_packages_tarball }}"
    format: tar
    mode: '0644'
  when: iso_data.version.split('.')[0] | int == bundler.rhel_major_version
