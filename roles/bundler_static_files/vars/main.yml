# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

tmp_uploads: /tmp/uploads
modified_iso_location: "/tmp/modified_iso"
original_rhel_iso_location: "/tmp/{{ bundler.rhel_iso_name }}"
original_satellite_iso_location: "/tmp/{{ bundler.satellite_iso_name }}"
nexus_image: harbor.global.lmco.com/ext.hub.docker.com/sonatype/nexus3:{{ bundler.nexus_image_version }}
bare_packages_file: "/tmp/bare_packages.txt"
global_nexus_domain: nexus.global.lmco.com
global_nexus_user: "{{ lookup('env', 'GLOBAL_NEXUS_USER') }}"
global_nexus_password: "{{ lookup('env', 'GLOBAL_NEXUS_PASSWORD') }}"
