# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        download_bundle_files.yml                                         #
# Version:                                                                        #
#               2025-08-05 espy & <PERSON><PERSON>                                    #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: download_bundle_files | Download lmco.galaxy image as lmco.galaxy.tar image name - {{ galaxy_image }}
  ansible.builtin.shell:
    cmd: |
      skopeo copy docker://{{ galaxy_image }} docker-archive:{{ package_files }}/lmco.galaxy.tar
  changed_when: true

- name: download_bundle_files | Download static files
  when: not bundler.distro == "sno"
  ansible.builtin.get_url:
    url: "https://{{ bundler.nexus_domain }}/{{ bundler.nexus_repo_path }}/{{ item }}"
    username: "{{ bundler.nexus_user }}"
    password: "{{ bundler.nexus_password }}"
    dest: "{{ package_files_galaxy_folder }}/{{ item }}"
    mode: "0644"
    timeout: 600
  loop:
    - "{{ bundler.ocp_rhel_iso_name if bundler.platform == 'baremetal' else [] }}"
    - "{{ bundler.nexus_image_file }}"

- name: download_bundle_files | Copy over additional files
  ansible.builtin.copy:
    src: "{{ additional_file }}"
    dest: "{{ package_files }}/"
    mode: "0755"
  loop: "{{ additional_files }}"
  loop_control:
    loop_var: additional_file
  vars:
    additional_files: "{{ bundler.additional_files.split(',') if bundler.additional_files else [] }}"

- name: download_bundle_files | Copy over run script
  ansible.builtin.template:
    src: big-bang.sh.j2
    dest: "{{ package_files }}/big-bang.sh"
    mode: "0755"

- name: download_bundle_files | Create secrets folder
  ansible.builtin.file:
    path: "{{ package_files }}/secrets"
    state: directory
    mode: "0755"

- name: download_bundle_files | Copy over default vault pass
  ansible.builtin.template:
    src: /home/<USER>/.vpw
    dest: "{{ package_files }}/secrets/vault_pass"
    mode: "0644"
