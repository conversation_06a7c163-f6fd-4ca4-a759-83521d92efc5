---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        testbed-deploy-improved.yml                                       #
# Version:                                                                        #
#               2025-08-27 Improved version with better Harbor handling           #
# Create Date:  2025-08-27                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Improved testbed-deploy.yml with better Harbor registry handling  #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #
include:
  - /gitlab/ansible-collection.yml

variables:
  GIT_STRATEGY: clone
  ENABLE_SAST_SCANS: "false"
  GALAXY_VERSION: "latest"
  BUILDER_LMI_IP: **************
  BUILDER_MGMT_IP: **************
  BUILDER_USER: galaxy
  STORAGE_BACKEND: nexus
  RESOURCE_GROUP: "NULL_GROUP"
  GALAXY_FOLDER: /data/${CLUSTER_NAME}-bundle-cicd-install
  HTTP_PORT: 8090
  VERBOSITY: ""
  ADDITIONAL_FILES: "$CI_PROJECT_DIR/host_vars,$CI_PROJECT_DIR/group_vars,$CI_PROJECT_DIR/files,$CI_PROJECT_DIR/inventory.yml"
  OPENSHIFT_VARS: "$CI_PROJECT_DIR/group_vars/openshift.yml"

generate_galaxy_bundle:
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:$GALAXY_VERSION
  stage: build
  timeout: 12h
  variables:
    ANSIBLE_FORCE_COLOR: "true"
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: always
    - when: manual
      allow_failure: true
  before_script:
    - echo "Setting up registry authentication and network configuration"
    # Backup original /etc/hosts before any modifications
    - cp /etc/hosts /etc/hosts.backup
    
    # Set up proxy environment variables for the entire session
    - export HTTP_PROXY="http://proxy-zsgov.external.lmco.com:80"
    - export HTTPS_PROXY="http://proxy-zsgov.external.lmco.com:80"
    - export NO_PROXY=".lmco.com,localhost,127.0.0.1"
    
    # Ensure Harbor registry resolves correctly (not to localhost)
    - echo "Checking Harbor registry DNS resolution"
    - nslookup harbor.global.lmco.com || echo "DNS lookup failed, will use IP"
    
    # Test network connectivity to Harbor registry through proxy
    - echo "Testing Harbor registry connectivity through proxy"
    - curl --proxy "$HTTP_PROXY" -I https://harbor.global.lmco.com/v2/ || echo "Harbor registry not reachable through proxy"
    
    # Create Docker config directory and authenticate
    - mkdir -p ~/.docker
    - echo "Authenticating with Harbor registry using skopeo"
    
    # Try authentication with proxy settings
    - |
      if ! skopeo login harbor.global.lmco.com --username ${HARBOR_US_USER} --password ${HARBOR_US_USER_CLI_SECRET}; then
        echo "Direct skopeo login failed, trying with proxy environment"
        HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
        skopeo login harbor.global.lmco.com --username ${HARBOR_US_USER} --password ${HARBOR_US_USER_CLI_SECRET}
      fi
    
    # Create Docker config.json for additional authentication
    - echo "{\"auths\":{\"harbor.global.lmco.com\":{\"auth\":\"$(echo -n ${HARBOR_US_USER}:${HARBOR_US_USER_CLI_SECRET} | base64 -w 0)\"}}}" > ~/.docker/config.json
    
    # Test registry connectivity before proceeding
    - echo "Testing registry connectivity"
    - |
      if ! skopeo inspect docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:${GALAXY_VERSION} --raw; then
        echo "Direct registry inspection failed, trying with proxy"
        HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY" \
        skopeo inspect docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:${GALAXY_VERSION} --raw || echo "Registry inspection failed even with proxy"
      fi
  script:
    - welcome
    - env
    
    # Export Harbor credentials as the environment variables expected by Ansible
    - export HARBOR_US_USER="${HARBOR_US_USER}"
    - export HARBOR_US_USER_CLI_SECRET="${HARBOR_US_USER_CLI_SECRET}"
    - export GLOBAL_NEXUS_USER="${HARBOR_US_USER}"
    - export GLOBAL_NEXUS_PASSWORD="${HARBOR_US_USER_CLI_SECRET}"
    
    # Create a custom /etc/hosts backup and protection script
    - |
      echo "Setting up /etc/hosts protection..."
      cp /etc/hosts /etc/hosts.original

      # Create a script that prevents Harbor from being added to localhost
      cat > /tmp/protect_hosts.sh << 'EOF'
      #!/bin/bash
      # This script monitors /etc/hosts and removes harbor.global.lmco.com from localhost line
      while true; do
        if grep -q "127.0.0.1.*harbor\.global\.lmco\.com" /etc/hosts; then
          echo "$(date): Detected Harbor registry in localhost line, removing..."
          # Remove harbor.global.lmco.com from the localhost line
          sed -i '/^127\.0\.0\.1/s/harbor\.global\.lmco\.com//g' /etc/hosts
          # Clean up multiple spaces
          sed -i '/^127\.0\.0\.1/s/  */ /g' /etc/hosts
          # Remove trailing spaces
          sed -i '/^127\.0\.0\.1/s/ *$//' /etc/hosts
          echo "$(date): Fixed /etc/hosts"
        fi
        sleep 2
      done
      EOF
      chmod +x /tmp/protect_hosts.sh

      # Start the protection script in background
      /tmp/protect_hosts.sh &
      PROTECT_PID=$!
      echo "Started /etc/hosts protection with PID: $PROTECT_PID"

    # Run the main bundle creation with DNS protection overrides and proxy settings
    - >
      HTTP_PROXY="$HTTP_PROXY" HTTPS_PROXY="$HTTPS_PROXY" NO_PROXY="$NO_PROXY"
      ansible-playbook -i localhost lmco.bundler.create_bundle.yml
      -e "@$OPENSHIFT_VARS"
      -e "{'cli_galaxy':{'version':'${GALAXY_VERSION}'}}"
      -e "{'bundle_name':'${CLUSTER_NAME}' }"
      -e "{'cli_bundler':{'additional_files':'${ADDITIONAL_FILES}'}}"
      -e "bundler={'default_registry_url':'galaxy-registry.local','default_nexus_url':'galaxy-nexus.local','nexus_domain':'galaxy-nexus.apps.galaxy-uge1-ocp02.us.lmco.com'}"
      -e "proxying_mirror=false"
      -e "domain_mappings=[]"
      $VERBOSITY

    # Clean up the protection script
    - |
      echo "Cleaning up /etc/hosts protection..."
      pkill -f "protect_hosts.sh" || true
      cp /etc/hosts.original /etc/hosts || true
      echo "Cleanup completed"
  tags:
    - aws
    - galaxy-xl
    - kubernetes

.deploy-cluster-from-bundle:
  stage: deploy
  image: harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy
  rules:
    - if: $CI_PIPELINE_SOURCE == "schedule"
      when: on_success
  before_script:
    - echo "Copy SSH key to .ssh" &&
      cp $GALAXY_SSH_KEY ~/.ssh/id_rsa &&
      chmod 600 ~/.ssh/id_rsa
    - >
      curl -s --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" "https://gitlab.global.lmco.com/api/v4/projects/7508/repository/files/scripts%2Fget_bundle.bash/raw?ref=main" > get_bundle.bash
    - >
      curl -s --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" "https://gitlab.global.lmco.com/api/v4/projects/7508/repository/files/scripts%2Fwait_for_port.sh/raw?ref=main" > wait_for_port.sh
    - scp get_bundle.bash $BUILDER_USER@$BUILDER_LMI_IP:~/get_bundle.sh
    - scp wait_for_port.sh $BUILDER_USER@$BUILDER_LMI_IP:~/wait_for_port.sh
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "
      chmod +x ~/get_bundle.sh &&
      HIVESTAR_USER=${REGISTRY_USER}
      HIVESTAR_PASSWORD=${REGISTRY_PASSWORD}
      ~/get_bundle.sh ${GALAXY_VERSION} ${GALAXY_FOLDER} ${CLUSTER_NAME} ${STORAGE_BACKEND}"
    - scp -rp ~/.ssh/id_rsa $BUILDER_USER@$BUILDER_LMI_IP:${GALAXY_FOLDER}/secrets/id_rsa
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "
      chmod +x ~/wait_for_port.sh &&
      ~/wait_for_port.sh ${HTTP_PORT}"
  script:
    - ssh $BUILDER_USER@$BUILDER_LMI_IP "cd ${GALAXY_FOLDER} && GALAXY_BUILD_PORT=${HTTP_PORT} GALAXY_BUILD_LMI_IP=${BUILDER_LMI_IP} GALAXY_BUILD_MGMT_IP=${BUILDER_MGMT_IP} EXTRA_ANSIBLE_ARGS='< /dev/null' ./openshift bash.sh"
  tags:
    - kraken
  timeout: 8h

.deploy-cluster:
  extends: .deploy-cluster-from-bundle
  resource_group: "$RESOURCE_GROUP"

deploy-cluster:
  extends: .deploy-cluster
  needs:
    - generate_galaxy_bundle

.manual-deploy:
  rules:
    - if: $CI_PIPELINE_SOURCE != "schedule"
      when: manual
  stage: test

deploy-manually:
  extends:
    - .deploy-cluster
    - .manual-deploy
