---
# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        role_list.yml                                                     #
# Version:                                                                        #
#               2025-08-05 Initial                                                #
# Create Date:  2025-08-05                                                        #
# Author:       <PERSON><PERSON>, <PERSON> (<EMAIL>)                             #
# Description:                                                                    #
#               Initial commit for role_list.yml                                  #
# Inputs:                                                                         #
#               None                                                              #
# Outputs:                                                                        #
#               None                                                              #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: role_list | Set default version if version not defined
  ansible.builtin.set_fact:
    galaxy_bundle_version: "default"
  when: product.version is not defined

- name: role_list | Set version for provider {{ product.provider }}
  ansible.builtin.set_fact:
    galaxy_bundle_version: "{{ product.version }}"
  when: product.version is defined

- name: role_list | Include roles from roles.yml
  ansible.builtin.include_vars:
    file: "/home/<USER>/.ansible/collections/ansible_collections/lmco/galaxy/roles/{{ product.name }}\
      /vars/{{ product.target }}/{{ product.provider }}/{{ product.profile }}/roles.yml"

- name: role_list |  Get galaxy_roles for version "{{ galaxy_bundle_version }}"
  ansible.builtin.set_fact:
    extracted_roles: "{{ (galaxy_bundle | selectattr('version', 'equalto', galaxy_bundle_version) | list)[0].galaxy_roles }}"

- name: role_list | Create a list of combined role names
  ansible.builtin.set_fact:
    combined_role_list: "{{ combined_role_list | default([]) + extracted_roles | unique }}"
