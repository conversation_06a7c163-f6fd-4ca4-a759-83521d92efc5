{# j2lint: disable=jinja-variable-lower-case #}
kind: ImageSetConfiguration
apiVersion: mirror.openshift.io/v1alpha2
storageConfig:
  local:
    path: {{ apps_install_path }}/mirror-redhat2galaxy
mirror:
  platform:
    architectures:
      - {{ architecture }}
    graph: true
    channels:
{% if bundler.distro == 'ocp' %}
    - name: {{ ocp_channel }}
      type: ocp
      minVersion: {{ bundler.openshift_version }}
      maxVersion: {{ bundler.openshift_version }}
{% endif %}
{% if bundler.distro == 'okd' %}
    - name: okd
      type: okd
      minVersion: {{ bundler.okd_version }}
      maxVersion: {{ bundler.okd_version }}
{% endif %}
{% if bundler.distro == 'ocp' and include_previous_ocp_versions %}
{%     for channel in bundler.ocp_channels | default([]) %}
    - name: {{ channel.name }}
      type: {{ channel.type }}
      minVersion: '{{ channel.minVersion }}'
      maxVersion: '{{ channel.maxVersion }}'
{%     endfor %}
{% endif %}
{% if  roi|length > 0 or
       coi|length > 0 or
       comoi|length > 0 or
       ibm|length > 0 or
       cs|length> 0  %}
  operators:
{% endif %}
{% if roi is defined %}
{%     if bundler.distro == 'ocp' and include_previous_ocp_versions %}
{%         for version in roi %}
  - catalog: registry.redhat.io/redhat/redhat-operator-index:v{{ version }}
    packages:
{%             for operator in roi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                 if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                 endif %}
{%                 if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                 endif %}
{%             endfor %}
{%         endfor %}
{%     else %}
{%         for version in roi %}
{%             if version == (bundler.openshift_version.split('.')[0:2] | join('.') | float) %}
  - catalog: registry.redhat.io/redhat/redhat-operator-index:v{{ version }}
    packages:
{%                 for operator in roi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                     if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                     endif %}
{%                     if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                     endif %}
{%                 endfor %}
{%             endif %}
{%         endfor %}
{%     endif %}
{% endif %}
{% if coi is defined %}
{%     if bundler.distro == 'ocp' and include_previous_ocp_versions %}
{%         for version in coi %}
  - catalog: registry.redhat.io/redhat/certified-operator-index:v{{ version }}
    packages:
{%             for operator in coi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                 if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                 endif %}
{%                 if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                 endif %}
{%             endfor %}
{%         endfor %}
{%     else %}
{%         for version in coi %}
{%             if version == (bundler.openshift_version.split('.')[0:2] | join('.') | float) %}
  - catalog: registry.redhat.io/redhat/certified-operator-index:v{{ version }}
    packages:
{%                 for operator in coi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                     if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                     endif %}
{%                     if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                     endif %}
{%                 endfor %}
{%             endif %}
{%         endfor %}
{%     endif %}
{% endif %}
{% if comoi is defined %}
{%     if bundler.distro == 'ocp' and include_previous_ocp_versions %}
{%         for version in comoi %}
  - catalog: registry.redhat.io/redhat/community-operator-index:v{{ version }}
    packages:
{%             for operator in comoi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                 if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                 endif %}
{%                 if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                 endif %}
{%             endfor %}
{%         endfor %}
{%     else %}
{%         for version in comoi %}
{%             if version == (bundler.openshift_version.split('.')[0:2] | join('.') | float) %}
  - catalog: registry.redhat.io/redhat/community-operator-index:v{{ version }}
    packages:
{%                 for operator in comoi[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                     if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                     endif %}
{%                     if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                     endif %}
{%                 endfor %}
{%             endif %}
{%         endfor %}
{%     endif %}
{% endif %}
{% if cs is defined %}
{%     if bundler.distro == 'ocp' and include_previous_ocp_versions %}
{%         for version in cs %}
{%             for operator in cs[version] %}
  - catalog: {{ operator.catalogSources[0].image }}
    packages:
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                 if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                 endif %}
{%                 if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                 endif %}
{%             endfor %}
{%         endfor %}
{%     else %}
{%         for version in cs %}
{%             if version == (bundler.openshift_version.split('.')[0:2] | join('.') | float) %}
{%                 for operator in cs[version] %}
  - catalog: {{ operator.catalogSources[0].image }}
    packages:
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%                     if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%                     endif %}
{%                     if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%                     endif %}
{%                 endfor %}
{%             endif %}
{%         endfor %}
{%     endif %}
{% endif %}
{% if ibm is defined %}
{%     if ibm | length > 0 %}
  - catalog: icr.io/cpopen/ibm-operator-catalog:latest
    packages:
{%     endif %}
{%     for version in ibm %}
{%         for operator in ibm[version] %}
    - name: {{ operator.name }}
      defaultChannel: {{ operator.channels[0].name }}
      channels:
        - name: {{ operator.channels[0].name }}
{%             if operator.channels[0].maxVersion is defined  %}
          maxVersion: {{ operator.channels[0].maxVersion }}
{%             endif %}
{%             if operator.channels[0].minVersion is defined %}
          minVersion: {{ operator.channels[0].minVersion }}
{%             endif %}
{%         endfor %}
{%     endfor %}
{% endif %}
  additionalImages:
{% for image in (add_img_list | rejectattr('name', 'contains', '.lmco.com')) %}
    - name: {{ image.name }}
{% endfor %}
  helm: {}
