#!/bin/bash

# Script to convert the Bundler Main Role documentation to PDF
# This script provides multiple methods to create a PDF from the markdown documentation

echo "=== Bundler Main Role Documentation PDF Converter ==="
echo

# Check if the markdown file exists
if [ ! -f "bundler-main-role-documentation.md" ]; then
    echo "❌ Error: bundler-main-role-documentation.md not found"
    echo "Please ensure the markdown file is in the current directory"
    exit 1
fi

echo "✅ Found bundler-main-role-documentation.md"
echo

# Method 1: Using pandoc (recommended)
echo "Method 1: Using pandoc (recommended)"
if command -v pandoc >/dev/null 2>&1; then
    echo "✅ pandoc is available"
    echo "Converting to PDF..."
    
    pandoc bundler-main-role-documentation.md \
        -o bundler-main-role-documentation.pdf \
        --pdf-engine=xelatex \
        --toc \
        --toc-depth=3 \
        --number-sections \
        --highlight-style=github \
        --geometry=margin=1in \
        --variable=fontsize:11pt \
        --variable=documentclass:article \
        --variable=papersize:letter
    
    if [ $? -eq 0 ]; then
        echo "✅ PDF created successfully: bundler-main-role-documentation.pdf"
        echo "   Size: $(ls -lh bundler-main-role-documentation.pdf | awk '{print $5}')"
    else
        echo "❌ pandoc conversion failed"
    fi
else
    echo "❌ pandoc not found"
    echo "   Install with: brew install pandoc (macOS) or apt-get install pandoc (Ubuntu)"
fi

echo

# Method 2: Using markdown-pdf (Node.js)
echo "Method 2: Using markdown-pdf (Node.js)"
if command -v markdown-pdf >/dev/null 2>&1; then
    echo "✅ markdown-pdf is available"
    echo "Converting to PDF..."
    
    markdown-pdf bundler-main-role-documentation.md \
        -o bundler-main-role-documentation-alt.pdf \
        --format A4 \
        --border 1in
    
    if [ $? -eq 0 ]; then
        echo "✅ Alternative PDF created: bundler-main-role-documentation-alt.pdf"
    else
        echo "❌ markdown-pdf conversion failed"
    fi
else
    echo "❌ markdown-pdf not found"
    echo "   Install with: npm install -g markdown-pdf"
fi

echo

# Method 3: Using wkhtmltopdf
echo "Method 3: Using wkhtmltopdf"
if command -v wkhtmltopdf >/dev/null 2>&1; then
    echo "✅ wkhtmltopdf is available"
    echo "Converting markdown to HTML first..."
    
    # Convert to HTML first
    if command -v pandoc >/dev/null 2>&1; then
        pandoc bundler-main-role-documentation.md \
            -o bundler-main-role-documentation.html \
            --standalone \
            --css=github.css \
            --highlight-style=github
        
        echo "Converting HTML to PDF..."
        wkhtmltopdf \
            --page-size A4 \
            --margin-top 1in \
            --margin-bottom 1in \
            --margin-left 1in \
            --margin-right 1in \
            --enable-local-file-access \
            bundler-main-role-documentation.html \
            bundler-main-role-documentation-html.pdf
        
        if [ $? -eq 0 ]; then
            echo "✅ HTML-based PDF created: bundler-main-role-documentation-html.pdf"
            # Clean up HTML file
            rm -f bundler-main-role-documentation.html
        else
            echo "❌ wkhtmltopdf conversion failed"
        fi
    else
        echo "❌ pandoc needed for HTML conversion"
    fi
else
    echo "❌ wkhtmltopdf not found"
    echo "   Install with: brew install wkhtmltopdf (macOS) or apt-get install wkhtmltopdf (Ubuntu)"
fi

echo

# Summary
echo "=== Summary ==="
echo "Available PDF files:"
for pdf in *.pdf; do
    if [ -f "$pdf" ]; then
        echo "  ✅ $pdf ($(ls -lh "$pdf" | awk '{print $5}'))"
    fi
done

echo
echo "=== Alternative Methods ==="
echo "If the above methods don't work, you can:"
echo "1. Copy the content from bundler-main-role-documentation.md"
echo "2. Paste it into any of these online converters:"
echo "   - https://www.markdowntopdf.com/"
echo "   - https://md2pdf.netlify.app/"
echo "   - https://cloudconvert.com/md-to-pdf"
echo "3. Or use VS Code with the 'Markdown PDF' extension"
echo "4. Or use Google Docs: paste markdown, then File → Download → PDF"

echo
echo "=== Installation Commands ==="
echo "To install the required tools:"
echo
echo "macOS (using Homebrew):"
echo "  brew install pandoc"
echo "  brew install wkhtmltopdf"
echo "  npm install -g markdown-pdf"
echo
echo "Ubuntu/Debian:"
echo "  sudo apt-get update"
echo "  sudo apt-get install pandoc texlive-xetex wkhtmltopdf"
echo "  npm install -g markdown-pdf"
echo
echo "RHEL/CentOS:"
echo "  sudo yum install pandoc wkhtmltopdf"
echo "  npm install -g markdown-pdf"

echo
echo "=== Complete ==="
