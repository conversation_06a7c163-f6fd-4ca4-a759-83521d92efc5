# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        setup_harbor_auth.yml                                             #
# Version:                                                                        #
#               2025-08-27 Initial                                                #
# Create Date:  2025-08-27                                                        #
# Author:       Augment Agent                                                     #
# Description:                                                                    #
#               Setup Harbor registry authentication for skopeo operations        #
# Inputs:                                                                         #
#               Environment variables: HARBOR_US_USER, HARBOR_US_USER_CLI_SECRET #
# Outputs:                                                                        #
#               Configured authentication for Harbor registry                     #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: setup_harbor_auth | Check if Harbor credentials are available
  ansible.builtin.set_fact:
    harbor_user: "{{ lookup('env', 'HARBOR_US_USER') | default(lookup('env', 'GLOBAL_NEXUS_USER'), true) }}"
    harbor_password: "{{ lookup('env', 'HARBOR_US_USER_CLI_SECRET') | default(lookup('env', 'GLOBAL_NEXUS_PASSWORD'), true) }}"

- name: setup_harbor_auth | Debug available credentials
  ansible.builtin.debug:
    msg: "Harbor user found: {{ harbor_user != '' }}, Password found: {{ harbor_password != '' }}"

- name: setup_harbor_auth | Skip authentication if no credentials
  ansible.builtin.debug:
    msg: "No Harbor credentials found, skipping authentication setup"
  when: harbor_user == "" or harbor_password == ""

- name: setup_harbor_auth | Create Docker config directory
  ansible.builtin.file:
    path: "{{ ansible_env.HOME }}/.docker"
    state: directory
    mode: "0700"

- name: setup_harbor_auth | Test Harbor registry connectivity
  ansible.builtin.uri:
    url: "https://harbor.global.lmco.com/v2/"
    method: GET
    status_code: [200, 401, 403]
    timeout: 30
  environment:
    HTTP_PROXY: "{{ bundler.http_proxy }}"
    HTTPS_PROXY: "{{ bundler.https_proxy }}"
    NO_PROXY: "{{ bundler.standard_no_proxy_addresses | join(',') }}"
  register: harbor_connectivity_test
  ignore_errors: true

- name: setup_harbor_auth | Display connectivity test results
  ansible.builtin.debug:
    msg: "Harbor registry connectivity test: {{ harbor_connectivity_test.status | default('Failed') }}"

- name: setup_harbor_auth | Login to Harbor registry with skopeo
  ansible.builtin.shell:
    cmd: |
      skopeo login harbor.global.lmco.com --username "{{ harbor_user }}" --password "{{ harbor_password }}"
  environment:
    HTTP_PROXY: "{{ bundler.http_proxy }}"
    HTTPS_PROXY: "{{ bundler.https_proxy }}"
    NO_PROXY: "{{ bundler.standard_no_proxy_addresses | join(',') }}"
  register: skopeo_login_result
  ignore_errors: true
  no_log: true
  when: harbor_user != "" and harbor_password != ""

- name: setup_harbor_auth | Create Docker config.json for Harbor authentication
  ansible.builtin.copy:
    content: |
      {
        "auths": {
          "harbor.global.lmco.com": {
            "auth": "{{ (harbor_user + ':' + harbor_password) | b64encode }}"
          }
        }
      }
    dest: "{{ ansible_env.HOME }}/.docker/config.json"
    mode: "0600"
  no_log: true
  when: harbor_user != "" and harbor_password != ""

- name: setup_harbor_auth | Display authentication status
  ansible.builtin.debug:
    msg: |
      Harbor authentication setup completed:
      - Credentials available: {{ harbor_user != "" and harbor_password != "" }}
      - Skopeo login: {{ 'Success' if (skopeo_login_result.rc | default(1)) == 0 else 'Failed' }}
      - Ready for image operations
