#############################################################################################
# Global Section                                                                            #
#############################################################################################
global
  daemon
  maxconn                   10000
  log     /dev/log local2 debug

#############################################################################################
# Defaults Section                                                                          #
#############################################################################################
defaults
  mode                      http
  option                    http-server-close
  option                    httplog
  option                    logasap
  timeout http-request      10m
  timeout queue             10m
  timeout connect           10m
  timeout client            10m
  timeout server            10m
  timeout http-keep-alive   10m
  timeout check             10m
  log                       global
  log-format "Status: %ST Server: %s TotalTime: %Tt BytesRead: %B Method: %HM URI: %HU"

frontend all_http
  bind *:80
  redirect scheme https code 301

frontend all_https
  bind *:443 ssl crt {{ apps_install_path }}/certs/tls.pem
  mode http
  acl {{ bundler.default_registry_url }}_host hdr(host) -i {{ bundler.default_registry_url }}
  acl {{ bundler.default_nexus_url }}_host    hdr(host) -i {{ bundler.default_nexus_url }}
  use_backend {{ bundler.default_registry_url }}_backend if {{ bundler.default_registry_url }}_host
  use_backend {{ bundler.default_nexus_url }}_backend if {{ bundler.default_nexus_url }}_host
{% for domain in domain_mappings %}
  acl is_external_{{ domain.destination }} hdr(host) -i {{ domain.destination }}
  use_backend {{ domain.destination }}_backend if is_external_{{ domain.destination }}
{% endfor %}
  default_backend {{ bundler.default_nexus_url }}_backend

backend {{ bundler.default_nexus_url }}_backend
  mode http
  server nexus 127.0.0.1:{{ bundler.nexus_http_port }}

backend {{ bundler.default_registry_url }}_backend
  mode http
  server registry 127.0.0.1:{{ bundler.registry_port }}

{% for domain in domain_mappings %}
backend {{ domain.destination }}_backend
  mode http
  option ssl-hello-chk
  http-request set-header Host {{ domain.reroute }}
  http-request set-header Authorization "Basic {{ (bundler.nexus_user + ':' + bundler.nexus_password) | b64encode }}"
  server {{ domain.destination }} {{ domain.reroute }}:443 ssl verify none
{% endfor %}
