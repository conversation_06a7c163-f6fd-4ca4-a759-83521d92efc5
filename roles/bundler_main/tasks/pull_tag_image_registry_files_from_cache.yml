# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        pull_tag_image_registry_files_from_cache.yml                      #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: pull_tag_image_registry_files_from_cache | Decode the content and set as a fact
  ansible.builtin.set_fact:
    imageset_data: >-
      {{
        lookup('file', 'additonal_image_list.yaml') |
        from_yaml |
        selectattr('name', 'contains', '.lmco.com') |
        map(attribute='name')
      }}

- name: pull_tag_image_registry_files_from_cache | Set aap image
  ansible.builtin.set_fact:
    aap_ee_image: "{{ aap_ee_image_name }}:{{ galaxy.version }}"

- name: pull_tag_image_registry_files_from_cache | Add aap image to image list
  ansible.builtin.set_fact:
    imageset_data: "{{ imageset_data + [aap_ee_image] }}"

- name: pull_tag_image_registry_files_from_cache | Pull the images from harbor
  loop: "{{ imageset_data | default([]) }}"
  containers.podman.podman_image:
    name: "{{ image }}"
  loop_control:
    loop_var: image
  register: pull_result
  until: pull_result is succeeded
  retries: 5
  delay: 10

- name: pull_tag_image_registry_files_from_cache | Retag the images from harbor to galaxy
  containers.podman.podman_tag:
    image: "{{ image }}"
    target_names:
      - "{{ default_registry_url }}/{{ image | regex_replace('^(.*?)/', '') }}"
  loop: "{{ imageset_data }}"
  loop_control:
    loop_var: image

- name: pull_tag_image_registry_files_from_cache | Push images to galaxy at {{ default_registry_url }}
  containers.podman.podman_image:
    name: "{{ default_registry_url }}/{{ image | regex_replace('^(.*?)/', '') }}"
    push: true
  loop: "{{ imageset_data }}"
  loop_control:
    loop_var: image
    label: "{{ default_registry_url }}/{{ image | regex_replace('^(.*?)/', '') }}"
  register: push_result
  until: push_result is succeeded
  retries: 5
  delay: 10

- name: pull_tag_image_registry_files_from_cache | Copy over image list to nexus data folder
  ansible.builtin.copy:
    src: additonal_image_list.yaml
    dest: "{{ nexus_data_dir }}/additonal_images.yaml"
    mode: "0644"
