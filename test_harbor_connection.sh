#!/bin/bash

# Test script to verify Harbor registry connectivity and authentication
# This script tests the same conditions that your GitLab CI pipeline will encounter

echo "=== Harbor Registry Connection Test ==="
echo "Date: $(date)"
echo

# Check if environment variables are set
echo "1. Checking environment variables..."
if [ -z "$HARBOR_US_USER" ]; then
    echo "   ❌ HARBOR_US_USER is not set"
    MISSING_VARS=true
else
    echo "   ✅ HARBOR_US_USER is set: $HARBOR_US_USER"
fi

if [ -z "$HARBOR_US_USER_CLI_SECRET" ]; then
    echo "   ❌ HARBOR_US_USER_CLI_SECRET is not set"
    MISSING_VARS=true
else
    echo "   ✅ HARBOR_US_USER_CLI_SECRET is set: [HIDDEN]"
fi

if [ "$MISSING_VARS" = true ]; then
    echo
    echo "Please set the required environment variables:"
    echo "export HARBOR_US_USER='your-username'"
    echo "export HARBOR_US_USER_CLI_SECRET='your-password'"
    exit 1
fi

echo

# Set proxy variables (matching your GitLab CI configuration)
export HTTP_PROXY="http://proxy-zsgov.external.lmco.com:80"
export HTTPS_PROXY="http://proxy-zsgov.external.lmco.com:80"
export NO_PROXY=".lmco.com,localhost,127.0.0.1"

echo "2. Testing network connectivity..."
echo "   Proxy settings:"
echo "   - HTTP_PROXY: $HTTP_PROXY"
echo "   - HTTPS_PROXY: $HTTPS_PROXY"
echo "   - NO_PROXY: $NO_PROXY"

# Test basic connectivity to Harbor registry
echo "   Testing Harbor registry connectivity..."
if curl --proxy "$HTTP_PROXY" -s -I https://harbor.global.lmco.com/v2/ | head -1 | grep -q "200\|401\|403"; then
    echo "   ✅ Harbor registry is reachable through proxy"
else
    echo "   ❌ Harbor registry is not reachable through proxy"
    echo "   Trying direct connection..."
    if curl -s -I https://harbor.global.lmco.com/v2/ | head -1 | grep -q "200\|401\|403"; then
        echo "   ✅ Harbor registry is reachable directly"
    else
        echo "   ❌ Harbor registry is not reachable directly either"
    fi
fi

echo

# Test skopeo authentication
echo "3. Testing skopeo authentication..."
if command -v skopeo >/dev/null 2>&1; then
    echo "   ✅ skopeo is available"
    
    # Test login
    echo "   Testing skopeo login..."
    if skopeo login harbor.global.lmco.com --username "$HARBOR_US_USER" --password "$HARBOR_US_USER_CLI_SECRET" 2>/dev/null; then
        echo "   ✅ skopeo login successful"
        
        # Test image inspection
        echo "   Testing image inspection..."
        if skopeo inspect docker://harbor.global.lmco.com/lmc.space.galaxy/lmco.galaxy:latest --raw >/dev/null 2>&1; then
            echo "   ✅ Image inspection successful"
        else
            echo "   ❌ Image inspection failed"
        fi
    else
        echo "   ❌ skopeo login failed"
    fi
else
    echo "   ❌ skopeo is not available"
    echo "   Please install skopeo to test authentication"
fi

echo

# Test Docker config creation
echo "4. Testing Docker config creation..."
mkdir -p ~/.docker
AUTH_STRING=$(echo -n "$HARBOR_US_USER:$HARBOR_US_USER_CLI_SECRET" | base64 -w 0)
cat > ~/.docker/config.json << EOF
{
  "auths": {
    "harbor.global.lmco.com": {
      "auth": "$AUTH_STRING"
    }
  }
}
EOF

if [ -f ~/.docker/config.json ]; then
    echo "   ✅ Docker config.json created successfully"
    echo "   Config file location: ~/.docker/config.json"
else
    echo "   ❌ Failed to create Docker config.json"
fi

echo

# Summary
echo "=== Test Summary ==="
echo "This test simulates the environment your GitLab CI pipeline will use."
echo "If all tests pass, your pipeline should be able to authenticate with Harbor registry."
echo
echo "Next steps:"
echo "1. Commit the updated Ansible roles"
echo "2. Run your existing GitLab CI pipeline"
echo "3. Monitor the 'generate_galaxy_bundle' job for successful Harbor authentication"
echo

echo "=== Test Complete ==="
