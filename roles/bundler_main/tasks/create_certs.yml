# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_certs.yml                                                  #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_certs | Create cert directory
  ansible.builtin.file:
    path: "{{ cert_directory }}"
    state: directory
    mode: '0755'

- name: create_certs | Copy over ssl config file
  ansible.builtin.template:
    src: openssl.conf.j2
    dest: /tmp/{{ dns_name }}.openssl.conf
    mode: '0644'
  delegate_to: localhost
  throttle: 1

- name: create_certs | Create certificates
  ansible.builtin.shell:
    cmd: |
      openssl req -config {{ dns_name }}.openssl.conf -new -nodes -x509 -days 180 -out {{ dns_name }}.tls.crt
      cat {{ dns_name }}.tls.crt {{ dns_name }}.tls.key > {{ dns_name }}.tls.pem
    chdir: "/tmp"
    creates: "{{ dns_name }}.tls.pem"
  delegate_to: localhost
  changed_when: true
  throttle: 1

- name: create_certs | Copy registry cert to destination directory
  ansible.builtin.copy:
    src: "/tmp/{{ dns_name }}.{{ ssl_file }}"
    dest: "{{ cert_directory }}/{{ ssl_file }}"
    mode: "0644"
  loop:
    - tls.crt
    - tls.key
    - tls.pem
    - openssl.conf
  loop_control:
    loop_var: ssl_file

- name: create_certs | Copy registry cert to system anchors
  ansible.builtin.copy:
    src: "/tmp/{{ dns_name }}.tls.crt"
    dest: /etc/pki/ca-trust/source/anchors/{{ dns_name }}.crt
    mode: "0400"
  notify: Update_ca_trust
  become: true
  become_user: root

- name: create_certs | Flush handlers
  ansible.builtin.meta: flush_handlers
