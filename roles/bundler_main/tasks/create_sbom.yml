# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        create_sbom.yml                                                   #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: create_sbom | Download file
  ansible.builtin.get_url:
    url: "https://{{ bundler.nexus_domain }}/repository/spdx/{{ spdx_file }}"
    dest: "/tmp/{{ spdx_file }}"
    url_username: "{{ bundler.nexus_user }}"
    url_password: "{{ bundler.nexus_password }}"
    mode: "0644"

- name: create_sbom | Extract file
  ansible.builtin.command:
    cmd: 'bzip2 -df /tmp/{{ spdx_file }}'
    creates: '/tmp/{{ spdx_file | regex_replace("\.bz2$", "") }}'

- name: create_sbom | Use Custom Module
  lmco.bundler.filter_sbom_packages:
    sbom: '/tmp/{{ spdx_file | regex_replace("\.bz2$", "") }}'
    package_list: '{{ lookup("file", role_path + "/../bundler_static_files/files/bare_os_install_packages-" + bundler.rhel_major_version | string + "." +
      bundler.rhel_minor_version | string + ".txt") | split + filtered_packages | sort | unique }}'
    output_sbom: '{{ package_files }}/filtered_{{ spdx_file | regex_replace("\.bz2$", "") }}'
