# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        mirror_registry_to_nexus.yml                                      #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: mirror_registry_to_nexus | Create config.json
  ansible.builtin.copy:
    content: "{{ combined_auths | to_nice_json }}"
    dest: "~/.docker/config.json"
    mode: "0600"
  vars:
    rh_json_content: "{{ ('{}' if proxying_mirror else lookup('file', bundler.redhat_registry_pull_secret_file)) | from_json }}"
    auth_to_add: "{{ lookup('template', 'config.json.j2') }}"
    combined_auths: "{{ rh_json_content | combine(auth_to_add, recursive=true) }}"

- name: mirror_registry_to_nexus | Pull the Additional Images from Harbor Cache
  ansible.builtin.include_tasks: pull_tag_image_registry_files_from_cache.yml

- name: mirror_registry_to_nexus | Create registry mirror config
  ansible.builtin.template:
    src: image-set-configuration.yaml.j2
    dest: "{{ apps_install_path }}/mirror-redhat2galaxy/image-set-configuration.yaml"
    mode: '0644'

- name: mirror_registry_to_nexus | Create ICSP for generic-0 with additionalImages
  ansible.builtin.template:
    src: generic-0_additional_images.j2
    dest: "{{ apps_install_path }}/mirror-redhat2galaxy/generic-0_additional_images.yaml.j2"
    mode: '0644'

- name: mirror_registry_to_nexus | Get sha for mirror
  ansible.builtin.shell:
    cmd: set -o pipefail && sha256sum {{ apps_install_path }}/mirror-redhat2galaxy/image-set-configuration.yaml | awk '{print $1}'
  register: mirror_sha
  changed_when: false

- name: mirror_registry_to_nexus | Display the contents of image-set-configuration.yaml
  ansible.builtin.debug:
    msg: "{{ lookup('file', apps_install_path + '/mirror-redhat2galaxy/image-set-configuration.yaml') }}"

- name: mirror_registry_to_nexus | Mirror from redhat to galaxy
  lmco.galaxy.long_running_task:
    cmd: |
      set -e
      umask 0002
      oc mirror \
        --config {{ apps_install_path }}/mirror-redhat2galaxy/image-set-configuration.yaml \
        --max-per-registry 10 \
        -v 6 \
        docker://{{ bundler.default_registry_url }}
      mv .oc-mirror.log oc-mirror-redhat2galaxy.log
    progress_command: |
      echo "Current output line: $(tail {{ apps_install_path }}/mirror-redhat2galaxy/.oc-mirror.log -n1 2>/dev/null)"
      echo "Current nexus data dir size: $(du -sh {{ nexus_data_dir }} 2>/dev/null | awk '{print $1}')"
    creates: "{{ apps_install_path }}/mirror-redhat2galaxy/mirror-{{ mirror_sha.stdout }}.tar"
    chdir: "{{ apps_install_path }}/mirror-redhat2galaxy"
  environment:
    HTTPS_PROXY: "{{ bundler.http_proxy }}"
    HTTP_PROXY: "{{ bundler.http_proxy }}"
    NO_PROXY: "{{ no_proxy }}"

- name: mirror_registry_to_nexus | Copy over records
  ansible.builtin.command:
    cmd: mv {{ apps_install_path }}/mirror-redhat2galaxy/{{ file_to_copy }} {{ nexus_data_dir }}/
    creates: "{{ nexus_data_dir }}/{{ file_to_copy }}"
  loop:
    - image-set-configuration.yaml
    - oc-mirror-redhat2galaxy.log
    - oc-mirror-workspace
  loop_control:
    loop_var: file_to_copy
    label: "{{ file_to_copy }}"

- name: mirror_registry_to_nexus | Get ICSP file name
  ansible.builtin.find:
    paths: "{{ nexus_data_dir }}/oc-mirror-workspace"
    patterns: imageContentSourcePolicy.*
    use_regex: true
    recurse: true
  register: image_content_source_policy_file_location

- name: mirror_registry_to_nexus | Record copy of original ICSP from oc mirror
  ansible.builtin.copy:
    src: "{{ image_content_source_policy_file_location.files[0].path }}"
    dest: "{{ nexus_data_dir }}/oc-mirror-original-icsp.yml"
    mode: "0644"

- name: mirror_registry_to_nexus | Remove first occurrence of section from file
  ansible.builtin.replace:
    path: "{{ image_content_source_policy_file_location.files[0].path }}"
    regexp: '^---(?:(?!---)[\s\S])*name: generic-0(?:(?!---)[\s\S])*?---$'
    replace: '---'

- name: mirror_registry_to_nexus | Remove empty lines from file
  ansible.builtin.lineinfile:
    path: "{{ image_content_source_policy_file_location.files[0].path }}"
    regexp: '^\s*$'
    state: absent
    mode: '0644'

- name: mirror_registry_to_nexus | Load file into variable
  ansible.builtin.slurp:
    src: "{{ apps_install_path }}/mirror-redhat2galaxy/generic-0_additional_images.yaml.j2"
  register: file_contents_generic_0

- name: mirror_registry_to_nexus | Display file contents
  ansible.builtin.debug:
    var: file_contents_generic_0.content|b64decode()
    verbosity: 2

- name: mirror_registry_to_nexus | Ansible replace block example
  ansible.builtin.blockinfile:
    path: "{{ image_content_source_policy_file_location.files[0].path }}"
    block: "{{ file_contents_generic_0.content | b64decode() }}"
    create: true
    state: present
    mode: '0644'

- name: mirror_registry_to_nexus | Add nexus sha file to nexus folder
  ansible.builtin.copy:
    dest: "{{ galaxy_nexus_repo_files_dir }}/nexus-sha.txt"
    mode: '0644'
    content: "{{ mirror_sha.stdout }}"

- name: mirror_registry_to_nexus | Add nexus sha version vars yaml
  ansible.builtin.lineinfile:
    dest: "{{ package_files_galaxy_folder }}/{{ bundler.verson_vars_file }}"
    mode: '0644'
    line: "nexus_sha: {{ mirror_sha.stdout }}"
    create: true
