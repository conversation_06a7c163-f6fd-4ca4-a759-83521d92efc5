---
apiVersion: operator.openshift.io/v1alpha1
kind: ImageContentSourcePolicy
metadata:
  name: generic-0
spec:
{% if img_set_list_icsp_map_modified %}
  repositoryDigestMirrors:
{%     for image in img_set_list_icsp_map_modified %}
  - mirrors:
{%         if image.split('/')[0:-1] | length == 0 %}
    - {{ galaxy.registry.initial_hostname }}/{{ image }}
{%         else %}
    - {{ galaxy.registry.initial_hostname }}/{{ image.split('/')[1:] | join('/') }}
{%         endif %}
    source: {{ image }}
{%     endfor %}
{% else %}
  repositoryDigestMirrors: []
{% endif %}
---