# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Create tmp folder
  ansible.builtin.file:
    path: "{{ tmp_uploads }}"
    state: directory
    mode: "0755"

- name: Get nexus image from harbor
  ansible.builtin.shell:
    cmd: |
      skopeo copy --src-creds {{ lookup('env', 'GLOBAL_NEXUS_USER') }}:{{ lookup('env', 'GLOBAL_NEXUS_PASSWORD') }} docker://{{ nexus_image }} docker-archive:{{ tmp_uploads }}/{{ bundler.nexus_image_file }}
      chmod 644 {{ tmp_uploads }}/{{ bundler.nexus_image_file }}
    creates: "{{ tmp_uploads }}/{{ bundler.nexus_image_file }}"
  environment:
    HTTP_PROXY: "{{ bundler.http_proxy }}"
    HTTPS_PROXY: "{{ bundler.https_proxy }}"
    NO_PROXY: "{{ bundler.standard_no_proxy_addresses | join(',') }}"
  when: update_nexus_image

- name: Create minimal os iso
  ansible.builtin.include_tasks: create_minimal_os_iso.yml
  when: update_os_iso

- name: Get all files
  ansible.builtin.find:
    paths:
      - "{{ tmp_uploads }}"
    file_type: file
    recurse: true
  register: files_to_upload

- name: Upload files to global nexus
  ansible.builtin.uri:
    url: "https://{{ global_nexus_domain }}/{{ bundler.nexus_repo_path }}/{{ item.path | regex_replace('^' + tmp_uploads + '/', '') }}"
    method: PUT
    user: "{{ global_nexus_user }}"
    password: "{{ global_nexus_password }}"
    headers:
      Content-Type: application/x-tar
    src: "{{ item.path }}"
    force_basic_auth: true
    remote_src: true
    status_code:
      - 201
  loop: "{{ files_to_upload.files }}"
  loop_control:
    label: "{{ item.path | regex_replace('^' + tmp_uploads + '/', '') }}"
