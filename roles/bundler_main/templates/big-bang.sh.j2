set -e
set -o pipefail

uidmap=$(awk -F: '/'$(whoami)'/{print $3}' /etc/subuid)
gidmap=$(awk -F: '/'$(whoami)'/{print $3}' /etc/subgid)

[ ${uidmap} -ge 4000000 -a ${gidmap} -ge 4000000 ] || {
  cat <<_EOF_
  The /etc/subuid and /etc/subgid file entries for the
  $(whoami) user need have a uid/gid allocatoin of at lesat 4000000.

  Please update the files appropriately and execute the following
  to allow the new allocatoins to take effect:

  Execute the following as the $(whoami) user:
    - Remove any existing galaxy images
    - Execute the podman migrate command:
      podman system migrate
_EOF_
  exit 1
}

output=$(podman load -i $(pwd)/lmco.galaxy.tar)
image=$(echo $output | sed -n -e 's/^Loaded image: //p')
podman tag $image galaxy
export GALAXY_BUILD_PORT=${GALAXY_BUILD_PORT:-8080}
if [ -z "${GALAXY_BUILD_LMI_IP}" ]; then
  interface=$(ip route show 0.0.0.0/0 | sort -k5n | head -n1 | awk '{print $5}')
  export GALAXY_BUILD_LMI_IP=$(ip -o -4 addr show dev "${interface}" | awk '{split($4,a,"/"); print a[1]}' | head -n1 )
fi
if [ -z "${GALAXY_BUILD_MGMT_IP}" ]; then
  export GALAXY_BUILD_MGMT_IP=${GALAXY_BUILD_LMI_IP}
fi

echo Using port ${GALAXY_BUILD_PORT}
echo Using LMI ip ${GALAXY_BUILD_LMI_IP}
echo Using MGMT ip ${GALAXY_BUILD_MGMT_IP}

if chcat c1 $(pwd)/galaxy_files; then
  galaxy_mode=z
else
  galaxy_mode=rw
fi

if chcat c1 $(pwd)/secrets/id_rsa; then
  rsa_mode=z
else
  rsa_mode=rw
fi

if chcat c1 $(pwd)/secrets/vault_pass; then
  vault_mode=z
else
  vault_mode=rw
fi

if chcat c1 $(pwd); then
  build_mode=z
else
  build_mode=rw
fi

podman run --rm -it \
--security-opt label=disable \
--device /dev/fuse \
--network slirp4netns \
--userns keep-id:uid=1000,gid=1000 \
--env GALAXY_BUILD* \
-v $(pwd)/galaxy_files:/galaxy_files:$galaxy_mode \
-v $(pwd)/secrets/id_rsa:/home/<USER>/.ssh/id_rsa:$rsa_mode \
-v $(pwd)/secrets/vault_pass:/home/<USER>/.vpw:$vault_mode \
-v $(pwd):/openshift_build:$build_mode \
-w /openshift_build \
-p ${GALAXY_BUILD_PORT}:8080 \
galaxy \
bash -c "httpd -f /home/<USER>/.httpd/httpd.conf && ansible-playbook -i /openshift_build/inventory.yml lmco.galaxy.galaxy_builder $EXTRA_ANSIBLE_ARGS" | tee deploy-$(date "+%Y-%m-%d_%H-%M-%S").out
