# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Display Bundler Inputs
  ansible.builtin.debug:
    msg:
      - galaxy - {{ galaxy | default({}) }}
      - default_bundler - {{ default_bundler | default({}) }}
      - bundler_base - {{ bundler_base | default({}) }}
      - default_derived_bundler - {{ default_derived_bundler | default({}) }}
      - derived_bundler - {{ derived_bundler | default({}) }}
      - galaxy_bundler - {{ galaxy_bundler | default({}) }}
      - lmco_bundler - {{ lmco_bundler | default({}) }}
      - cli_bundler - {{ cli_bundler | default({}) }}
    verbosity: 1

- name: One time set of ocp variables
  ansible.builtin.set_fact:
    bundler: "{{ derived_bundler | default({}) |
      combine(galaxy_bundler | default({}), recursive=true) |
      combine(lmco_bundler | default({}), recursive=true) |
      combine(cli_bundler | default({}), recursive=true) }}"
