# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        configure_nexus.yml                                               #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: configure_nexus | Get auto created password
  ansible.builtin.slurp:
    path: "{{ nexus_data_dir }}/admin.password"
  register: encoded_galaxy_nexus_password

- name: configure_nexus | Change default password
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/security/users/{{ bundler.galaxy_nexus_user }}/change-password"
    method: "PUT"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ encoded_galaxy_nexus_password.content | b64decode }}"
    force_basic_auth: true
    body: "{{ bundler.galaxy_nexus_password }}"
    body_format: raw
    headers:
      Content-Type: "text/plain"
    status_code:
      - 204

- name: configure_nexus | Write down new default password in file
  ansible.builtin.copy:
    content: "{{ bundler.galaxy_nexus_password }}"
    dest: "{{ nexus_data_dir }}/admin.password"
    mode: "0600"

- name: configure_nexus | Add anonymous access
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/security/anonymous"
    method: "PUT"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      enabled: true
      userId: anonymous
      realmName: NexusAuthorizingRealm
    body_format: json

- name: configure_nexus | Get list of existing repositories
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories"
    method: "GET"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    return_content: true
  register: list_of_existing_repos

- name: configure_nexus | Delete existing repositories
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories/{{ repo_to_delete.name }}"
    method: "DELETE"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    status_code:
      - 204
  loop: "{{ list_of_existing_repos.json }}"
  loop_control:
    loop_var: repo_to_delete
    label: "{{ repo_to_delete.name }}"

- name: configure_nexus | Get ID of Outreach Managment Capability
  ansible.builtin.uri:
    url: "{{ default_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: capability_Capability
      method: read
      data:
      type: rpc
      tid: 997
    body_format: json
    return_content: true
  register: capabilties_response

- name: configure_nexus | Disable Outreach Management capability
  ansible.builtin.uri:
    url: "{{ default_nexus_url }}/service/extdirect"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      action: capability_Capability
      method: disable
      data:
        - "{{ outreach_capability_id }}"
      type: rpc
      tid: 998
    body_format: json
  vars:
    outreach_capability_id: capabilties_response.json.result.data | selectattr('typeId', 'equalto', 'OutreachManagementCapability') | map(attribute='id')

- name: configure_nexus | Create docker registry
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories/docker/hosted"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      name: galaxy-registry
      online: true
      storage:
        blobStoreName: default
        strictContentTypeValidation: true
        writePolicy: "ALLOW"
      cleanup: null
      component:
        proprietaryComponents: false
      docker:
        v1Enabled: false
        forceBasicAuth: true
        httpPort: "{{ bundler.registry_port }}"
        httpsPort: null
        subdomain: null
    body_format: json
    status_code:
      - 201

- name: configure_nexus | Create raw binary repository
  ansible.builtin.uri:
    url: "{{ galaxy_nexus_api }}/repositories/raw/hosted"
    method: "POST"
    user: "{{ bundler.galaxy_nexus_user }}"
    password: "{{ bundler.galaxy_nexus_password }}"
    force_basic_auth: true
    body:
      name: "{{ bundler.galaxy_nexus_repo_name }}"
      online: true
      storage:
        blobStoreName: default
        strictContentTypeValidation: false
        writePolicy: "ALLOW"
      cleanup: null
      component:
        proprietaryComponents: false
      raw:
        contentDisposition: "ATTACHMENT"
    body_format: json
    status_code:
      - 201
