# ------------------------------------------------------------------------------- #
#                                                                                 #
# Title:        main.yml                                                          #
# Version:                                                                        #
#               2025-21-07 espy                                                   #
# Create Date:  2025-21-07                                                        #
# Author:       Espy                                                              #
# Description:                                                                    #
#               This file contains code for the galaxy project                    #
# Inputs:                                                                         #
#               N/A                                                               #
# Outputs:                                                                        #
#               N/A                                                               #
#                                                                                 #
# ------------------------------------------------------------------------------- #

- name: Variable check
  ansible.builtin.include_role:
    name: lmco.galaxy.galaxy_common
    tasks_from: variable_check

- name: Setup container galaxy folder link to package files
  ansible.builtin.include_tasks: setup_container_galaxy_folder_link.yml

- name: Block for Download Assets
  environment:
    NO_PROXY: "{{ no_proxy }}"
    no_proxy: "{{ no_proxy }}"
  block:
    - name: Download Assets
      ansible.builtin.include_tasks: download_assets.yml

- name: Create Dynamic Imageset based on OCP Galaxy Products
  ansible.builtin.include_tasks: create_dynamic_imageset.yml

- name: Setup container
  ansible.builtin.include_tasks: setup_container.yml

- name: Setup haproxy
  ansible.builtin.include_tasks: setup_haproxy.yml

- name: Setup nexus repository
  ansible.builtin.include_tasks: create_nexus_repository_manager.yml

- name: Configure Nexus
  ansible.builtin.include_tasks: configure_nexus.yml

- name: Get baremetal installer
  ansible.builtin.include_tasks: get_baremetal_installer.yml

- name: Get coreos files
  ansible.builtin.include_tasks: get_coreos_files.yml
  when: bundler.platform == 'baremetal'

- name: Get DNF Repo
  ansible.builtin.include_tasks: get_dnf_repo_files.yml
  vars:
    dnf_repo_name: "{{ iso_data.name }}"
    packages_file_name: "ocp_install_packages.txt"
  loop: '{{ bundler.dnf_isos }}'
  loop_control:
    label: "{{ iso_data.name }}"
    loop_var: iso_data

- name: Mirror images from internet to local nexus registry
  ansible.builtin.include_tasks: mirror_registry_to_nexus.yml

- name: Upload nexus galaxy files to nexus galaxy repo
  ansible.builtin.include_tasks: upload_to_nexus.yml

- name: Package nexus data
  ansible.builtin.include_tasks: package_nexus_data.yml

- name: Download Bundle Files
  ansible.builtin.include_tasks: download_bundle_files.yml

- name: Upload galaxy bundle
  ansible.builtin.include_tasks: upload_galaxy_bundle.yml
